{"version": 2, "builds": [{"src": "frontend/package.json", "use": "@vercel/static-build", "config": {"distDir": "frontend/dist"}}, {"src": "api/**/*.js", "use": "@vercel/node"}], "routes": [{"src": "/api/(.*)", "dest": "/api/$1"}, {"src": "/health", "dest": "/api/health"}, {"src": "/(.*\\.(js|css|png|jpg|jpeg|gif|svg|ico|woff|woff2|ttf|eot))", "dest": "/frontend/dist/$1"}, {"src": "/landing.html", "dest": "/frontend/dist/landing.html"}, {"src": "/react_app.html", "dest": "/frontend/dist/react_app.html"}, {"src": "/app", "dest": "/frontend/dist/react_app.html"}, {"src": "/n8n", "dest": "/frontend/dist/react_app.html"}, {"src": "/", "dest": "/frontend/dist/landing.html"}, {"src": "/(.*)", "dest": "/frontend/dist/react_app.html"}], "env": {"NODE_ENV": "production"}, "installCommand": "npm run install:all", "buildCommand": "npm run build", "devCommand": "npm run dev"}